/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  images: {
    remotePatterns: [
      { hostname: "upload.wikimedia.org" },
      { hostname: "liz-generic-files.s3.us-east-2.amazonaws.com" },
      { hostname: "example.com" },
      { hostname: "fakeimg.ryd.tools" },
    ],
  },
  async rewrites() {
    return [
      {
        source: "/api-oneepr/:path*",
        destination: `${process.env.API}/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;

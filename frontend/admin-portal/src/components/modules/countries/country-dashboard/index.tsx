"use client";

import { ModuleContent } from "@/components/common/module-content";
import { PreviewCommitmentDialog } from "@/components/common/preview/preview-commitment-dialog";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { ServiceSetupProvider, useServiceSetup } from "@/hooks/use-service-setup";
import { getPackagingServices } from "@/lib/api/packaging-services";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Edit } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { CountryContent } from "../service-setup/country-content";
import { CountryReportSetsCard } from "./report-sets";
import { CountryRepresentativeTiersAndOtherCostsCard } from "./representative-tiers-and-other-costs";
import { CountryRequiredInformationsCard } from "./required-informations";
import { Skeleton } from "@/components/ui/skeleton";
import { PackagingTurnover } from "./packaging-turnover";
import { PackagingPerformance } from "./packaging-performance";
import { PackagingTonsReported } from "./packaging-tons-reported";

interface CountryDashboardModuleProps {
  countryCode: string;
}

function CountryDashboard({ countryCode }: CountryDashboardModuleProps) {
  const { country } = useServiceSetup();

  const { paramValues, changeParam } = useQueryFilter(["packaging-service"]);

  const selectedPackagingServiceId = Number(paramValues["packaging-service"]) || null;

  function handleChangePackagingService(serviceId: number) {
    changeParam("packaging-service", serviceId.toString());
  }

  const { data: packagingServices, isLoading: isLoadingPackagingServices } = useQuery({
    queryKey: ["packaging-services", countryCode],
    queryFn: async () => {
      const packagingServices = await getPackagingServices(country.id);

      if (!!packagingServices.length) {
        changeParam("packaging-service", packagingServices[0].id.toString());
      }

      return packagingServices;
    },
  });

  const selectedPackagingService = packagingServices?.find((service) => service.id === selectedPackagingServiceId);

  return (
    <>
      <CountryContent country={country} description={`Overview this country's performance or edit it's details`} />
      <ModuleContent containerClassName="bg-surface-03">
        <div className="flex items-start justify-between mb-5">
          <h2 className="text-primary text-3xl font-bold">License Service</h2>
          <Link href={`/countries/${countryCode}/service-setup`}>
            <Button type="button" variant="filled" size="small" color="yellow" leadingIcon={<Edit />}>
              Service setup
            </Button>
          </Link>
        </div>
        <div className="flex items-center gap-2">
          <PreviewCommitmentDialog countryCode={countryCode}>
            <Button
              type="button"
              variant="text"
              color="light-blue"
              size="small"
              className="underline underline-offset-2"
            >
              Preview Commitment Assessment
            </Button>
          </PreviewCommitmentDialog>
        </div>
        <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full my-8" />
        {isLoadingPackagingServices && (
          <div className="flex items-center gap-4 mb-6">
            <Skeleton className="h-10 w-24 px-5 py-2 rounded-2xl" />
            <Skeleton className="h-10 w-24 px-5 py-2 rounded-2xl" />
            <Skeleton className="h-10 w-24 px-5 py-2 rounded-2xl" />
          </div>
        )}
        {!!selectedPackagingService && (
          <>
            <div className="flex items-center gap-4 mb-6">
              {packagingServices?.map((packagingService) => (
                <button
                  key={packagingService.id}
                  data-selected={packagingService.id === selectedPackagingServiceId}
                  className="text-xl font-bold px-5 py-2 rounded-2xl text-primary bg-on-primary data-[selected=true]:text-white data-[selected=true]:bg-primary"
                  onClick={() => handleChangePackagingService(packagingService.id)}
                >
                  {packagingService.name}
                </button>
              ))}
            </div>
            <div className="w-full grid grid-cols-1 lg:grid-cols-12 gap-6">
              <PackagingPerformance packagingService={selectedPackagingService} />
              <PackagingTurnover packagingService={selectedPackagingService} />
              <PackagingTonsReported packagingService={selectedPackagingService} />
              <CountryReportSetsCard packagingServiceId={Number(selectedPackagingServiceId)} />
              <CountryRequiredInformationsCard />
              <CountryRepresentativeTiersAndOtherCostsCard />
            </div>
          </>
        )}
      </ModuleContent>
    </>
  );
}

export function CountryDashboardModule({ countryCode }: CountryDashboardModuleProps) {
  return (
    <ServiceSetupProvider countryCode={countryCode}>
      <CountryDashboard countryCode={countryCode} />
    </ServiceSetupProvider>
  );
}

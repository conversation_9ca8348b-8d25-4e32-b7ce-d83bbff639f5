"use client";

import { ModuleContent } from "@/components/common/module-content";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { useMutation, useQuery } from "@tanstack/react-query";
import { PreviewCommitmentDialog } from "@/components/common/preview/preview-commitment-dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { ServiceSetupProvider } from "@/hooks/use-service-setup";
import { getCountryByCode, updateCountry } from "@/lib/api/countries";
import { getServiceSetupStatusByCountryCode } from "@/lib/api/service-setups";
import { queryClient } from "@/lib/react-query";
import { Country } from "@/types/country";
import { Error } from "@interzero/oneepr-react-ui/Icon";
import { enqueueSnackbar } from "notistack";
import { CountryContent } from "./country-content";
import { CriteriasDrawer } from "./criterias-drawer";
import { ServiceSetupObligationCheck } from "./obligation-check";
import { ServiceSetupPackagingServices } from "./packaging-services";
import { ServiceSetupReportFrequencies } from "./report-frequencies";
import { ServiceSetupReportSets } from "./report-sets";
import { ServiceSetupRepresentativeTiersAndOtherCosts } from "./representative-tiers-and-other-costs";
import { ServiceSetupRequiredInformation } from "./required-information";
import { useEffect } from "react";

interface CountryServiceSetupModuleProps {
  countryCode: string;
}

export function CountryServiceSetupModule({ countryCode }: CountryServiceSetupModuleProps) {
  const { data: country } = useQuery({
    queryKey: ["country", countryCode],
    queryFn: () => getCountryByCode(countryCode),
  });

  return (
    <>
      <CountryContent country={country} description="Edit this country's services" />
      <ModuleContent containerClassName="flex-1 bg-surface-03 relative relative">
        <div className="mb-10">
          <div className="flex items-start justify-between mb-5">
            <h2 className="text-primary text-3xl font-bold">Service setup</h2>
            {!!country && <CountryPublishControl country={country} />}
          </div>
          <p className="text-primary text-xl mb-4">To add a new service provide the following information:</p>
          <PreviewCommitmentDialog countryCode={countryCode}>
            <div>
              <Button type="button" variant="text" size="small" color="light-blue">
                Preview Commitment Assessment
              </Button>
            </div>
          </PreviewCommitmentDialog>
          <p className="text-support-blue underline underline-offset-2"></p>
          <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full my-8" />
        </div>
        <ServiceSetupProvider countryCode={countryCode}>
          <div className="space-y-10">
            <ServiceSetupPackagingServices />
            <ServiceSetupObligationCheck />
            <ServiceSetupReportSets />
            <ServiceSetupReportFrequencies />
            <ServiceSetupRepresentativeTiersAndOtherCosts />
            <ServiceSetupRequiredInformation />
          </div>
          <CriteriasDrawer />
        </ServiceSetupProvider>
      </ModuleContent>
    </>
  );
}

function CountryPublishControl({ country }: { country: Country }) {
  const { isLoading: isLoadingCountry } = useQuery({
    queryKey: ["country", country.code],
    queryFn: () => getCountryByCode(country.code),
  });

  const { data: serviceSetupStatus, isLoading: isLoadingServiceSetupStatus } = useQuery({
    queryKey: ["service-setup-status", country.code],
    queryFn: () => getServiceSetupStatusByCountryCode(country.code),
  });

  const isPublished = country?.is_published;

  const { mutate: togglePublish, isPending: isTogglingPublish } = useMutation({
    mutationFn: (isPublished: boolean) => updateCountry(country.id, { is_published: isPublished }),
  });

  function publishCountry() {
    togglePublish(true, {
      onSuccess: () => {
        enqueueSnackbar("Country published", { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["country", country.code] });
      },
    });
  }

  function unpublishCountry() {
    togglePublish(false, {
      onSuccess: () => {
        enqueueSnackbar("Country unpublished", { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["country", country.code] });
      },
    });
  }

  useEffect(() => {
    (async () => {
      if (!country || !serviceSetupStatus) return;

      if (country.is_published && !serviceSetupStatus.completed) {
        await updateCountry(country.id, { is_published: false });
        queryClient.invalidateQueries({ queryKey: ["country", country.code] });
        enqueueSnackbar("Country unpublished because service setup is not completed", { variant: "info" });
      }
    })();
  }, [country, serviceSetupStatus]);

  if (isLoadingCountry || isLoadingServiceSetupStatus) return <Skeleton className="w-32 h-10 rounded-full" />;

  if (!isPublished) {
    return (
      <div className="flex items-center gap-2">
        {!serviceSetupStatus?.completed && serviceSetupStatus?.message && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <Error className="size-5 fill-error transition-all duration-300" />
              </TooltipTrigger>
              <TooltipContent className="bg-error text-white">
                <p>{serviceSetupStatus?.message}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <div>
              <Button
                type="button"
                variant="filled"
                size="small"
                color="yellow"
                disabled={isTogglingPublish || !serviceSetupStatus?.completed}
              >
                {isTogglingPublish ? "Publishing..." : "Publish country"}
              </Button>
            </div>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Publish country?</AlertDialogTitle>
              <AlertDialogDescription>Publishing will make the country available to customers.</AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction color="yellow" onClick={publishCountry} disabled={isTogglingPublish}>
                Publish
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    );
  }

  return (
    <AlertDialog>
      <AlertDialogTrigger>
        <Button type="button" variant="filled" size="small" color="dark-blue" disabled={isTogglingPublish}>
          {isTogglingPublish ? "Unpublishing..." : "Unpublish country"}
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Unpublish country?</AlertDialogTitle>
          <AlertDialogDescription>Unpublishing will make the country unavailable to customers.</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={unpublishCountry} disabled={isTogglingPublish}>
            Unpublish
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}

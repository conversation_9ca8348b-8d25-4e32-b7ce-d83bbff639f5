"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useServiceSetup } from "@/hooks/use-service-setup";
import { getServiceSetupPackagingServices } from "@/lib/api/service-setups";
import { PackagingService } from "@/types/service-setup/packaging-service";
import { CheckCircle } from "@interzero/oneepr-react-ui/Icon";
import { useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { CriteriasButton } from "../criterias-drawer/criterias-button";

export function ServiceSetupObligationCheck() {
  const { paramValues, changeParam, deleteAllParams } = useQueryFilter(["step"]);

  const isSelected = paramValues.step === "obligation-check";

  function handleOpenStep() {
    changeParam("step", "obligation-check");
  }

  function handleCloseStep() {
    deleteAllParams();
  }

  const { country } = useServiceSetup();

  const { data: packagingServices, isFetching } = useQuery({
    queryKey: ["service-setup-packaging-services", country.code],
    queryFn: () => getServiceSetupPackagingServices(country.code),
  });

  const isComplete = packagingServices && !!packagingServices.length && packagingServices.every((p) => p.has_criteria);

  if (isFetching) {
    return (
      <div className="bg-background rounded-[20px] p-8 space-y-2" id="obligation-check-step">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-full" />
      </div>
    );
  }

  return (
    <div
      data-selected={isSelected}
      data-complete={isComplete}
      className="group bg-background rounded-[20px] p-8 cursor-pointer data-[selected=true]:cursor-default"
      onClick={() => !isSelected && handleOpenStep()}
      id="obligation-check-step"
    >
      <div className="flex items-center justify-between">
        <h3 className="text-primary text-2xl font-bold">2. Obligation check</h3>
        {!isSelected && isComplete && <CheckCircle className="size-6 fill-success transition-all duration-300" />}
      </div>

      {isSelected && <ObligationCheckForm packagingServices={packagingServices || []} onCloseStep={handleCloseStep} />}
    </div>
  );
}

interface ObligationCheckFormProps {
  packagingServices: PackagingService[];
  onCloseStep: () => void;
}

function ObligationCheckForm({ packagingServices }: ObligationCheckFormProps) {
  return (
    <div className="mt-6 space-y-6">
      {packagingServices.map((service) => (
        <div key={service.id} className="space-y-6">
          <div className="flex items-center justify-between">
            <p className="text-primary">{service.name}</p>
          </div>
          <div className="pl-10 w-full space-y-6">
            <CriteriasButton
              criteriaType="PACKAGING_SERVICE"
              packagingServiceId={service.id}
              hasCriteria={!!service.has_criteria}
              criteriaText="Obligation Criteria"
              shouldOpenCriteriasDrawer={() => {
                if (!service.id) {
                  enqueueSnackbar("Please save the packaging service first and add a criteria later", {
                    variant: "warning",
                  });
                  return false;
                }
                return true;
              }}
            />
          </div>
          <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
        </div>
      ))}
    </div>
  );
}

"use client";

import { FractionIcon } from "@/components/ui/fraction-icon";
import { FractionInput } from "@/components/ui/fraction-input";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { CheckboxInput } from "@/components/ui/checkbox";
import { Button } from "@interzero/oneepr-react-ui/Button";
import { Add, EditCircle, Error, KeyboardArrowDown, KeyboardArrowLeft } from "@interzero/oneepr-react-ui/Icon";
import { Input } from "@interzero/oneepr-react-ui/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { memo, useState } from "react";
import {
  Controller,
  useForm,
  useFormContext,
  useWatch,
  type UseFormRegister,
  type Control,
  type FieldErrors,
  type UseFormSetValue,
  type UseFormClearErrors,
  type UseFormSetError,
} from "react-hook-form";
import { z } from "zod";
import { FormPriceList, ReportSetFormData, reportSetPriceListFormSchema } from "./report-set-form-provider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { NEAR_YEARS } from "@/components/modules/price-lists/price-list-form/price-list-form-provider";
import { formatCurrency } from "@/utils/format-currency";
import { enqueueSnackbar } from "notistack";
import { format } from "date-fns";
import { DownRightIcon } from "@/components/ui/icons/DownRightIcon";
import { ReportSetMode } from "@/types/service-setup/report-set";

/**
 * MARK: Main component
 *
 * Manages price lists for report sets. Handles the display,
 * creation, and editing of price lists within a report set form context.
 *
 * Features:
 * - Displays existing price lists in collapsible details sections
 * - Shows price list information (license year, start date, pricing details)
 * - Supports different pricing models based on report set mode
 * - Provides create/edit functionality with form validation
 * - Handles success notifications for CRUD operations
 *
 * Switches between list view and form view based on user actions:
 * - List view: Shows all price lists with edit buttons and creation option
 * - Form view: Shows the price list creation/editing form
 */
export function ReportSetPriceLists() {
  const {
    formState: { errors },
    control,
    setValue,
    getValues,
  } = useFormContext<ReportSetFormData>();

  const [isCreatingPriceList, setIsCreatingPriceList] = useState(false);
  const [editingPriceListIndex, setEditingPriceListIndex] = useState<number | undefined>(undefined);

  function handlePriceListSubmit(data: ReportSetPriceListFormData) {
    const formattedPriceList: FormPriceList = {
      ...data,
      items: Object.entries(data.items).map(([fractionCode, item]) => ({
        id: undefined,
        fraction_code: fractionCode,
        price: item.price,
      })),
    };

    if (editingPriceListIndex !== undefined) {
      const priceList = getValues("price_lists")[editingPriceListIndex];

      if (!priceList) return;

      setValue(`price_lists.${editingPriceListIndex}`, formattedPriceList);
      setIsCreatingPriceList(false);
      setEditingPriceListIndex(undefined);
      enqueueSnackbar("Price list updated successfully", { variant: "success" });
      return;
    }

    setValue("price_lists", [...getValues("price_lists"), formattedPriceList]);
    setIsCreatingPriceList(false);
    setEditingPriceListIndex(undefined);
    enqueueSnackbar("Price list created successfully", { variant: "success" });
  }

  const priceLists = useWatch({ control, name: "price_lists" });

  const isEditingPriceList = editingPriceListIndex !== undefined;
  const mode = getValues("mode");
  return (
    <div className="w-full space-y-8">
      {!isCreatingPriceList && !isEditingPriceList && (
        <div className="space-y-8">
          <div className="flex items-center justify-between">
            <p className="text-primary text-xl">Price lists</p>
            {!!errors.price_lists && (
              <div className="flex items-center gap-2">
                <Error className="size-5 fill-error" />
                <p className="text-error text-sm">{errors.price_lists.message}</p>
              </div>
            )}
          </div>
          {priceLists && priceLists.length > 0 ? (
            <>
              <div className="space-y-6">
                {priceLists.map((priceList, priceListIndex) => (
                  <details key={`${priceList.title}-${priceListIndex}`} className="w-full group">
                    <summary className="flex items-center justify-between gap-4 hover:cursor-pointer">
                      <div className="flex items-center gap-4">
                        <KeyboardArrowDown
                          width={24}
                          className="sidebar-chevron ml-auto fill-support-blue transition-all duration-300 group-open:rotate-180"
                        />
                        <p className="text-primary text-md font-bold underline-offset-2">{priceList.title}</p>
                        {!!errors.price_lists?.[priceListIndex] && (
                          <div className="flex-none flex items-center gap-2">
                            <Error className="size-5 fill-error" />
                          </div>
                        )}
                      </div>
                      <Button
                        type="button"
                        variant="text"
                        color="light-blue"
                        size="small"
                        leadingIcon={<EditCircle />}
                        onClick={() => setEditingPriceListIndex(priceListIndex)}
                      >
                        Edit
                      </Button>
                    </summary>
                    <div className="py-4 pl-10">
                      <div className="flex items-center gap-10">
                        <div className="space-y-1">
                          <p className="text-primary">License year: </p>
                          <span className="block text-tonal-dark-cream-30">{priceList.license_year}</span>
                        </div>
                        <div className="space-y-1">
                          <p className="text-primary">Start: </p>
                          <span className="block text-tonal-dark-cream-30">
                            {format(new Date(priceList.start_date), "dd/MM/yyyy")}
                          </span>
                        </div>
                        <div className="space-y-1 flex-1">
                          {mode === ReportSetMode.NO_REPORTING ? (
                            <>
                              <p className="text-primary">Flat rate price: </p>
                              <span className="block text-tonal-dark-cream-30">
                                {formatCurrency(priceList?.fixed_price || 0)}
                              </span>
                            </>
                          ) : (
                            <>
                              <p className="text-primary">Price type: </p>
                              {priceList.type === "FIXED_PRICE" && (
                                <span className="block text-tonal-dark-cream-30">
                                  {formatCurrency(priceList?.fixed_price || 0)} - Fixed Price
                                </span>
                              )}
                              {priceList.type === "PRICE_PER_VOLUME_BASE_PRICE" && (
                                <span className="block text-tonal-dark-cream-30">
                                  {formatCurrency(priceList?.base_price || 0)} - Price per volume (base price)
                                </span>
                              )}
                              {priceList.type === "PRICE_PER_VOLUME_MINIMUM_FEE" && (
                                <span className="block text-tonal-dark-cream-30">
                                  {formatCurrency(priceList?.minimum_fee || 0)} - Price per volume (minimum fee)
                                </span>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </details>
                ))}
              </div>
              <hr className="text-tonal-dark-cream-80" />
            </>
          ) : null}
          <Button
            type="button"
            variant="text"
            color="light-blue"
            size="small"
            leadingIcon={<Add />}
            onClick={() => setIsCreatingPriceList(true)}
          >
            Create new price list
          </Button>
        </div>
      )}
      {(isCreatingPriceList || isEditingPriceList) && (
        <ReportSetPriceListForm
          mode={getValues("mode")}
          priceList={isEditingPriceList ? getValues("price_lists")[editingPriceListIndex] : undefined}
          onCancel={() => {
            setIsCreatingPriceList(false);
            setEditingPriceListIndex(undefined);
          }}
          onSubmit={handlePriceListSubmit}
        />
      )}
    </div>
  );
}

/**
 * MARK: Form container
 *
 * A form component for creating and editing price lists within report sets.
 * Handles different pricing models based on the report set mode:
 *
 * - NO_REPORTING mode: Simple flat rate pricing with basic fields
 * - PLATFORM mode: Complex pricing with multiple options:
 *   - Fixed price
 *   - Volume-based pricing (base price/minimum fee)
 */
interface ReportSetPriceListFormProps {
  mode: ReportSetMode;
  priceList?: FormPriceList;
  onCancel: () => void;
  onSubmit: (data: ReportSetPriceListFormData) => void;
}

const createReportSetPriceListFormSchema = reportSetPriceListFormSchema
  .extend({
    items: z
      .record(
        z.string(),
        z.object({
          fraction_code: z.string(),
          price: z.number().default(0),
        })
      )
      .optional()
      .default({}),
  })
  .superRefine((data, ctx) => {
    if (data.type === "FIXED_PRICE" && !data.fixed_price) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Fixed price is required",
        path: ["fixed_price"],
      });
    }

    if (data.type !== "FIXED_PRICE" && !Object.keys(data.items).length) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "Fraction prices are required",
        path: ["items"],
      });
    }

    if (data.type === "PRICE_PER_VOLUME_BASE_PRICE") {
      const hasBasePriceSelected = data.base_price !== null && data.base_price !== undefined;
      const hasMinimumFeeSelected = data.minimum_fee !== null && data.minimum_fee !== undefined;

      if (!hasBasePriceSelected && !hasMinimumFeeSelected) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "At least one option (base price or minimum fee) must be selected for volume-based pricing",
          path: ["base_price"],
        });
      }

      if (hasBasePriceSelected && (data.base_price === null || data.base_price === 0)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Base price value is required",
          path: ["base_price"],
        });
      }

      if (hasMinimumFeeSelected && (data.minimum_fee === null || data.minimum_fee === 0)) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Minimum fee value is required",
          path: ["minimum_fee"],
        });
      }
    }
  });

type ReportSetPriceListFormData = z.infer<typeof createReportSetPriceListFormSchema>;

function ReportSetPriceListForm({ mode, priceList, onCancel, onSubmit }: ReportSetPriceListFormProps) {
  const {
    register,
    formState: { errors },
    reset,
    handleSubmit,
    control,
    setValue,
    setError,
    clearErrors,
  } = useForm<ReportSetPriceListFormData>({
    resolver: zodResolver(createReportSetPriceListFormSchema),
    defaultValues: {
      ...priceList,
      license_year: priceList?.license_year || NEAR_YEARS[4],
      start_date: priceList?.start_date ? new Date(priceList.start_date).toISOString().split("T")[0] : undefined,
      type: priceList?.type || "FIXED_PRICE",
      items: priceList?.items.reduce(
        (acc, item) => {
          acc[item.fraction_code] = item;
          return acc;
        },
        {} as ReportSetPriceListFormData["items"]
      ),
    },
  });

  function handleCancel() {
    reset();
    onCancel();
  }

  function handleFormSubmit(data: ReportSetPriceListFormData) {
    onSubmit(data);
  }

  const priceType = useWatch({ control, name: "type" });

  useWatch({ control, name: "items" });
  return (
    <div>
      <Button
        type="button"
        variant="text"
        color="light-blue"
        size="small"
        leadingIcon={<KeyboardArrowLeft />}
        onClick={() => handleCancel()}
      >
        Back
      </Button>
      <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full my-6" />
      <div className="space-y-6">
        <p className="text-primary text-xl">Create price list</p>
        <div className="space-y-10">
          {/* content */}
          {mode === ReportSetMode.NO_REPORTING ? (
            <ReportSetPriceListFormNoReportingContent
              register={register}
              control={control}
              errors={errors}
              priceType={priceType}
            />
          ) : (
            <ReportSetPriceListFormPlatformContent
              register={register}
              control={control}
              errors={errors}
              setValue={setValue}
              clearErrors={clearErrors}
              setError={setError}
              priceType={priceType}
            />
          )}
          {/* Footer */}
          <div className="flex items-center justify-end gap-4">
            <Button type="button" variant="text" color="light-blue" size="medium" onClick={() => handleCancel()}>
              Cancel
            </Button>
            <Button
              form="create-price-list-form"
              type="submit"
              variant="filled"
              color={!!Object.keys(errors).length ? "red" : "dark-blue"}
              size="medium"
              onClick={handleSubmit(handleFormSubmit)}
            >
              {!!priceList ? "Save price list" : "Create price list"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * MARK: Form Content - No Reporting
 * Component for handling price list form in no reporting mode.
 * Only displays basic fields: title, license year, start date, and fixed price.
 */
interface ReportSetPriceListFormNoReportingContentProps {
  register: UseFormRegister<ReportSetPriceListFormData>;
  control: Control<ReportSetPriceListFormData>;
  errors: FieldErrors<ReportSetPriceListFormData>;
  priceType: ReportSetPriceListFormData["type"];
}
const ReportSetPriceListFormNoReportingContent = memo(function ReportSetPriceListFormNoReportingContent({
  register,
  control,
  errors,
  priceType,
}: ReportSetPriceListFormNoReportingContentProps) {
  return (
    <>
      {/* Title */}
      <Input
        label="Title *"
        placeholder="Price list title"
        {...register("title")}
        variant={errors.title ? "error" : "default"}
        errorMessage={errors.title?.message}
      />
      {/* Year, Date, Price */}
      <div className="w-full grid grid-cols-1 md:grid-cols-6 gap-6">
        <div className="col-span-2">
          <div className="space-y-2">
            <label htmlFor="serviceType" className="text-primary text-base">
              License year *
            </label>
            <Controller
              control={control}
              name="license_year"
              render={({ field: { onChange, onBlur, value } }) => (
                <Select
                  key={value}
                  onValueChange={(v) => onChange(Number(v))}
                  value={String(value)}
                  defaultValue={String(value)}
                >
                  <SelectTrigger onBlur={onBlur}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {NEAR_YEARS.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
        </div>
        <div className="col-span-2">
          <Input
            label="Start date *"
            type="date"
            {...register("start_date")}
            variant={errors.start_date ? "error" : "default"}
            errorMessage={errors.start_date?.message}
          />
        </div>
        <div className="col-span-2">
          <label className="text-primary text-base font-centra mb-2 font-normal block">Flat rate price *</label>
          <Controller
            name={"fixed_price"}
            control={control}
            disabled={priceType !== "FIXED_PRICE"}
            render={({ field }) => (
              <FractionInput {...field} type="currency" value={field.value || undefined} error={!!errors.fixed_price} />
            )}
          />
        </div>
      </div>
    </>
  );
});

/**
 * MARK: Form Content - Platform
 * Component for handling price list form in platform mode.
 * Displays full form with pricing type options: fixed price, volume-based pricing with base price/minimum fee,
 * and detailed fraction pricing for nested fraction hierarchies.
 */
interface ReportSetPriceListFormPlatformContentProps {
  register: UseFormRegister<ReportSetPriceListFormData>;
  control: Control<ReportSetPriceListFormData>;
  errors: FieldErrors<ReportSetPriceListFormData>;
  setValue: UseFormSetValue<ReportSetPriceListFormData>;
  clearErrors: UseFormClearErrors<ReportSetPriceListFormData>;
  setError: UseFormSetError<ReportSetPriceListFormData>;
  priceType: ReportSetPriceListFormData["type"];
}
const ReportSetPriceListFormPlatformContent = memo(function ReportSetPriceListFormPlatformContent({
  register,
  control,
  errors,
  setValue,
  clearErrors,
  setError,
  priceType,
}: ReportSetPriceListFormPlatformContentProps) {
  const generalForm = useFormContext<ReportSetFormData>();

  const fractions = useWatch({ control: generalForm.control, name: "fractions" });

  function handlePriceTypeChange(newType: ReportSetPriceListFormData["type"]) {
    setValue("items", {});

    if (newType === "FIXED_PRICE") {
      setValue("base_price", null);
      setValue("minimum_fee", null);
      clearErrors("base_price");
      clearErrors("minimum_fee");
      clearErrors("items");
      return;
    }

    if (!fractions?.length) {
      setError("items", { message: "The fractions need to be registered before the price lists" });
    }

    if (newType === "PRICE_PER_VOLUME_BASE_PRICE") {
      setValue("fixed_price", null);
      clearErrors("fixed_price");
      return;
    }

    setValue("items", {});
  }
  return (
    <>
      {/* Title */}
      <Input
        label="Title *"
        placeholder="Price list title"
        {...register("title")}
        variant={errors.title ? "error" : "default"}
        errorMessage={errors.title?.message}
      />
      {/* Year, Date, Price */}
      <div className="w-full grid grid-cols-1 md:grid-cols-6 gap-6">
        <div className="col-span-2">
          <div className="space-y-2">
            <label htmlFor="serviceType" className="text-primary text-base">
              License year *
            </label>
            <Controller
              control={control}
              name="license_year"
              render={({ field: { onChange, onBlur, value } }) => (
                <Select
                  key={value}
                  onValueChange={(v) => onChange(Number(v))}
                  value={String(value)}
                  defaultValue={String(value)}
                >
                  <SelectTrigger onBlur={onBlur}>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {NEAR_YEARS.map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>
        </div>
        <div className="col-span-2">
          <Input
            label="Start date *"
            type="date"
            {...register("start_date")}
            variant={errors.start_date ? "error" : "default"}
            errorMessage={errors.start_date?.message}
          />
        </div>
      </div>
      {/* Year, Date, Price */}
      <div className="w-full space-y-4">
        <label className="text-primary text-base font-centra mb-2">Select a price type for this fraction set</label>
        <div className="w-full flex flex-col gap-4">
          <div className="flex items-center gap-4"></div>
        </div>
        <div className="w-full">
          <Controller
            control={control}
            name="type"
            render={({ field: { onChange, value } }) => (
              <RadioGroup
                value={value}
                onValueChange={(newValue) => {
                  onChange(newValue);
                  handlePriceTypeChange(newValue as ReportSetPriceListFormData["type"]);
                }}
              >
                <div className="w-full flex flex-col gap-4">
                  <div className="flex items-center gap-4">
                    <label className="h-14 w-full md:w-72 flex items-center gap-3 text-primary cursor-pointer py-3 lg:text-nowrap">
                      <RadioGroupItem value="FIXED_PRICE" className="block flex-none" />
                      Fixed price
                    </label>
                    <div className="h-14">
                      <Controller
                        name={"fixed_price"}
                        control={control}
                        disabled={priceType !== "FIXED_PRICE"}
                        render={({ field }) => (
                          <FractionInput
                            {...field}
                            type="currency"
                            value={field.value || undefined}
                            error={!!errors.fixed_price}
                          />
                        )}
                      />
                    </div>
                  </div>

                  <div className="flex flex-col gap-4">
                    <div className="flex items-center gap-4">
                      <label className="h-14 w-full md:w-72 flex items-center gap-3 text-primary cursor-pointer py-3 lg:text-nowrap">
                        <RadioGroupItem value="PRICE_PER_VOLUME_BASE_PRICE" className="block flex-none" />
                        Volume-based pricing (base price)
                      </label>
                    </div>
                    {priceType === "PRICE_PER_VOLUME_BASE_PRICE" && (
                      <div className="ml-8 space-y-4">
                        <div className="flex items-center gap-4">
                          <Controller
                            name="base_price"
                            control={control}
                            render={({ field: { value, onChange, ...field } }) => {
                              const checked = typeof value === "number";
                              return (
                                <>
                                  <div className="w-full md:w-64">
                                    <CheckboxInput
                                      {...field}
                                      checked={checked}
                                      onChange={() => {
                                        if (checked) {
                                          onChange(null);
                                        } else {
                                          onChange(0);
                                        }
                                      }}
                                      label="Base price"
                                    />
                                  </div>
                                  <div className="h-14">
                                    <FractionInput
                                      {...field}
                                      onChange={(v) => {
                                        value !== null && onChange(v);
                                      }}
                                      type="currency"
                                      value={value || undefined}
                                      error={!!errors.base_price}
                                      disabled={!checked}
                                    />
                                  </div>
                                </>
                              );
                            }}
                          />
                        </div>
                        <div className="flex items-center gap-4">
                          <Controller
                            name="minimum_fee"
                            control={control}
                            render={({ field: { value, onChange, ...field } }) => {
                              const checked = typeof value === "number";
                              return (
                                <>
                                  <div className="w-full md:w-64">
                                    <CheckboxInput
                                      {...field}
                                      checked={checked}
                                      onChange={() => {
                                        if (checked) {
                                          onChange(null);
                                        } else {
                                          onChange(0);
                                        }
                                      }}
                                      label="Minimum fee"
                                    />
                                  </div>
                                  <div className="h-14">
                                    <FractionInput
                                      {...field}
                                      onChange={(v) => {
                                        value !== null && onChange(v);
                                      }}
                                      type="currency"
                                      value={value || undefined}
                                      error={!!errors.minimum_fee}
                                      disabled={!checked}
                                    />
                                  </div>
                                </>
                              );
                            }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </RadioGroup>
            )}
          />
        </div>
        {!!errors.items?.message && (
          <div className="flex items-center gap-2">
            <Error className="size-5 fill-error" />
            <p className="text-error text-sm">{String(errors.items?.message)}</p>
          </div>
        )}
      </div>
      {/* Base price settings */}
      {priceType !== "FIXED_PRICE" && fractions && (
        <div className="space-y-8">
          {fractions.map((firstLevelFraction) => (
            <div key={firstLevelFraction.code} className="w-full">
              <div className="w-full py-4 flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <FractionIcon size="medium" iconUrl={firstLevelFraction.fraction_icon.image_url} />
                  <p className="text-primary text-lg font-bold flex-1">{firstLevelFraction.name}</p>
                </div>
                <div className="flex items-center gap-3">
                  <div className="text-primary">Cost estimation price (€) *</div>
                  <input
                    type="hidden"
                    {...register(`items.${firstLevelFraction.code}.fraction_code`)}
                    value={firstLevelFraction.code}
                  />
                  <Controller
                    name={`items.${firstLevelFraction.code}.price`}
                    control={control}
                    render={({ field }) => (
                      <FractionInput
                        {...field}
                        type="fraction-currency"
                        error={errors.items?.[firstLevelFraction.code]?.price?.message}
                      />
                    )}
                  />
                </div>
              </div>
              {priceType === "PRICE_PER_VOLUME_BASE_PRICE" && !!firstLevelFraction.children.length && (
                <div className="px-4 py-6 bg-surface-02 rounded-sm">
                  <div className="space-y-6">
                    <div className="flex items-center gap-4 justify-between">
                      <div className="flex items-center gap-4">
                        <DownRightIcon className="stroke-tonal-dark-cream-80 -mt-4" />
                        <p className="text-tonal-dark-cream-40">Sub-fractions (level 02)</p>
                      </div>
                      <div className="flex items-center gap-2">
                        {!!errors.items?.[firstLevelFraction.code]?.price && <Error className="w-6 h-6 fill-error" />}
                      </div>
                    </div>
                    {!!firstLevelFraction.children.length &&
                      firstLevelFraction.children.map((secondLevelFraction, secondLevelFractionIndex) => (
                        <div key={secondLevelFractionIndex} className="space-y-6">
                          <div className="pl-8 space-y-6">
                            <div className="grid grid-cols-6 gap-6">
                              <div className="col-span-2 space-y-1">
                                <div className="flex items-end gap-2 text-primary text-sm">
                                  Name
                                  <span className="text-tonal-dark-cream-40 italic">Sub-fractions (02)</span>
                                </div>
                                <Input
                                  style={{ backgroundColor: "transparent" }}
                                  placeholder="Name"
                                  value={secondLevelFraction.name}
                                  enabled={false}
                                />
                              </div>
                              {!secondLevelFraction.children.length && (
                                <div className="col-span-2">
                                  <input
                                    type="hidden"
                                    {...register(`items.${secondLevelFraction.code}.fraction_code`)}
                                    value={secondLevelFraction.code}
                                  />
                                  <Controller
                                    name={`items.${secondLevelFraction.code}.price`}
                                    control={control}
                                    render={({ field }) => (
                                      <FractionInput
                                        label="Reporting price (€) *"
                                        {...field}
                                        type="fraction-currency"
                                        error={errors.items?.[secondLevelFraction.code]?.price?.message}
                                      />
                                    )}
                                  />
                                </div>
                              )}
                            </div>
                            {!!secondLevelFraction.children.length && (
                              <div className="space-y-4">
                                <div className="flex items-center justify-between gap-4">
                                  <div className="flex items-center gap-4">
                                    <DownRightIcon className="stroke-tonal-dark-cream-80 -mt-4" />
                                    <p className="text-tonal-dark-cream-40">Sub-fractions (level 03)</p>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {!!errors.items?.[secondLevelFraction.code] && (
                                      <Error className="w-6 h-6 fill-error" />
                                    )}
                                  </div>
                                </div>
                                {secondLevelFraction.children.map((thirdLevelFraction, thirdLevelFractionIndex) => (
                                  <div key={thirdLevelFractionIndex} className="space-y-6">
                                    <div className="pl-8">
                                      <div className="grid grid-cols-5 gap-6">
                                        <div className="col-span-2 space-y-1">
                                          <div className="flex items-end text-sm gap-2 text-primary">
                                            Name
                                            <span className="text-tonal-dark-cream-40 italic">
                                              Sub-fractions (level 03)
                                            </span>
                                          </div>
                                          <Input
                                            style={{ backgroundColor: "transparent" }}
                                            placeholder="Name"
                                            value={thirdLevelFraction.name}
                                            enabled={false}
                                          />
                                        </div>
                                        <div className="col-span-2">
                                          <input
                                            type="hidden"
                                            {...register(`items.${thirdLevelFraction.code}.fraction_code`)}
                                            value={thirdLevelFraction.code}
                                          />
                                          <Controller
                                            name={`items.${thirdLevelFraction.code}.price`}
                                            control={control}
                                            render={({ field }) => (
                                              <FractionInput
                                                label="Reporting price (€) *"
                                                {...field}
                                                type="fraction-currency"
                                                error={errors.items?.[thirdLevelFraction.code]?.price?.message}
                                              />
                                            )}
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                          <div className="h-[1px] w-full bg-tonal-dark-cream-80" />
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </>
  );
});

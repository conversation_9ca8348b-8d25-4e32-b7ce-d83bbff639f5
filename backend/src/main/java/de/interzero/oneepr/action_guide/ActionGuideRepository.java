package de.interzero.oneepr.action_guide;

import de.interzero.oneepr.customer.contract.Contract;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

@Repository
public interface ActionGuideRepository extends JpaRepository<ActionGuide, Integer> {

    /**
     * Finds all action guides for a specific contract that have not been soft-deleted.
     *
     * @param contractId The ID of the contract to filter by.
     * @return A list of non-deleted action guides for the given contract.
     */
    List<ActionGuide> findAllByContract_IdAndDeletedAtIsNull(Integer contractId);

    /**
     * Performs a bulk update to reactivate multiple ActionGuide entities at once.
     * <p>
     * This sets the contractStatus to 'ACTIVE' and updates the 'updatedAt' timestamp
     * for all action guides whose IDs are in the provided list.
     *
     * @param ids A list of ActionGuide entity IDs to reactivate.
     * @param now The current timestamp to set for the 'updatedAt' field.
     */
    @Modifying
    @Query("UPDATE ActionGuide ag SET ag.contractStatus = 'ACTIVE', ag.updatedAt = :now WHERE ag.id IN :ids")
    void reactivateAllByIds(@Param("ids") List<Integer> ids,
                            @Param("now") Instant now);

    /**
     * Finds all non-deleted action guides for a given list of contracts and eagerly
     * fetches their associated terminations.
     *
     * @param contracts A list of {@link Contract} entities to find action guides for.
     * @return A list of {@link ActionGuide} entities with their {@code termination} property initialized.
     */
    @Query("SELECT ag FROM ActionGuide ag LEFT JOIN FETCH ag.termination WHERE ag.contract IN :contracts AND ag.deletedAt IS NULL")
    @SuppressWarnings("UnusedReturnValue")
    List<ActionGuide> findByContractInAndDeletedAtIsNullFetchingTermination(@Param("contracts") List<Contract> contracts);
}

package de.interzero.oneepr.action_guide;

import de.interzero.oneepr.customer.customer.dto.ActionGuideDetailsDto;
import de.interzero.oneepr.customer.termination.TerminationMapper;
import org.mapstruct.Mapper;

/**
 * A MapStruct interface for mapping an {@link ActionGuide} entity to its detailed DTO.
 * The implementation of this interface is generated automatically at compile time.
 * <p>
 * This mapper corresponds to the Prisma query:
 * `include: { termination: true }`
 * It uses the {@link TerminationMapper} to handle the nested termination object.
 */
@Mapper(
        componentModel = "spring",
        uses = {TerminationMapper.class}
)
public interface ActionGuideMapper {

    /**
     * Maps an {@link ActionGuide} entity to an {@link ActionGuideDetailsDto}.
     * <p>
     * MapStruct will automatically handle the following:
     * <ul>
     *   <li>Directly maps fields with matching names (e.g., id, countryName).</li>
     *   <li>Maps the transient getter methods {@code getContractId()} and {@code getTerminationId()} to the DTO's fields.</li>
     *   <li>Delegates the mapping of the {@code termination} field to the {@link TerminationMapper}.</li>
     * </ul>
     * The {@code priceList} and {@code nextSteps} collections are ignored, matching the Prisma query's logic.
     *
     * @param actionGuide The source ActionGuide entity.
     * @return The mapped {@link ActionGuideDetailsDto}.
     */
    ActionGuideDetailsDto toDetailsDto(ActionGuide actionGuide);
}
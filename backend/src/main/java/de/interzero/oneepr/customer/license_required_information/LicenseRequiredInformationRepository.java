package de.interzero.oneepr.customer.license_required_information;

import de.interzero.oneepr.customer.contract.Contract;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link LicenseRequiredInformation} entities.
 */
@Repository
public interface LicenseRequiredInformationRepository extends JpaRepository<LicenseRequiredInformation, Integer> {

    List<LicenseRequiredInformation> findAllByLicense_IdAndDeletedAtIsNullOrderByIdAsc(Integer licenseId);

    /**
     * Finds all non-deleted required information records for a specific contract.
     * It uses an entity graph to eagerly fetch related entities to avoid N+1 query problems.
     *
     * @param contractId The ID of the contract to filter by.
     * @return A list of {@link LicenseRequiredInformation} entities.
     */
    List<LicenseRequiredInformation> findAllByContract_IdAndDeletedAtIsNullOrderByIdAsc(Integer contractId);

    Optional<LicenseRequiredInformation> findByIdAndDeletedAtIsNull(Integer id);

    Optional<LicenseRequiredInformation> findWithPermissionsByIdAndDeletedAtIsNull(Integer id);

    Optional<LicenseRequiredInformation> findWithDeclineAndContractByIdAndDeletedAtIsNull(Integer id);

    /**
     * Finds a single LicenseRequiredInformation entity by its ID, ensuring it has not been soft-deleted,
     * and eagerly fetches its associated 'contract' and the contract's 'customer' in a single query.
     *
     * @param id The primary key of the LicenseRequiredInformation entity.
     * @return An Optional containing the entity with the specified relations, or an empty Optional if not found.
     */
    @Query(
            "SELECT lri FROM LicenseRequiredInformation lri " + "LEFT JOIN FETCH lri.contract c " + "LEFT JOIN FETCH c.customer " + "LEFT JOIN FETCH lri.decline " + "WHERE lri.id = :id AND lri.deletedAt IS NULL"
    )
    Optional<LicenseRequiredInformation> findWithContractAndCustomerByIdAndDeletedAtIsNull(@Param("id") Integer id);

    /**
     * Finds all non-deleted license required information records for a given list of contracts.
     * <p>
     * This is a derived query method. Spring Data JPA automatically generates the
     * implementation based on the method name.
     *
     * @param contracts A list of {@link Contract} entities.
     * @return A list of {@link LicenseRequiredInformation} entities linked to the provided contracts.
     */
    @SuppressWarnings("UnusedReturnValue")
    List<LicenseRequiredInformation> findByContractInAndDeletedAtIsNull(List<Contract> contracts);
}
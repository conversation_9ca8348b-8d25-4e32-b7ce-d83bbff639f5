package de.interzero.oneepr.customer.contract.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DTO for partially updating an existing Contract.
 * Origin: This DTO is a direct translation of the UpdateContractDto from the NestJS source, which uses the PartialType utility.
 * Purpose: It is used as the request body for the contract update endpoint, allowing for partial updates of a contract's properties.
 * Function: Represents a set of optional fields that can be used to modify an existing contract. By extending CreateContractDto, it inherits all its fields, but for a partial update, none are strictly required.
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateContractDto extends CreateContractDto {

}
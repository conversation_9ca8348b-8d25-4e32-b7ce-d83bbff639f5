package de.interzero.oneepr.customer.termination;

import de.interzero.oneepr.customer.file.FileMapper;
import de.interzero.oneepr.customer.termination.dto.TerminationDetailsDto;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;

/**
 * A MapStruct interface for mapping a {@link Termination} entity to its detailed DTO.
 * The implementation of this interface is generated automatically at compile time.
 * <p>
 * This mapper corresponds to the Prisma query:
 * `include: { files: { where: { deleted_at: null } } }`
 * <p>
 * It uses an {@link AfterMapping} method to filter the nested 'files' collection,
 * ensuring that only non-deleted files are included in the final DTO,
 * perfectly replicating the original query's logic.
 */
@Mapper(
        componentModel = "spring",
        uses = {FileMapper.class}
)
public interface TerminationMapper {

    /**
     * Maps a {@link Termination} entity to a {@link TerminationDetailsDto}.
     * <p>
     * MapStruct will automatically map the direct properties and will use the
     * {@link FileMapper} to map the nested 'files' collection. The crucial
     * filtering of this collection is handled by the `applyFiltering` method.
     *
     * @param termination The source Termination entity.
     * @return The mapped {@link TerminationDetailsDto}.
     */
    TerminationDetailsDto toDetailsDto(Termination termination);

    /**
     * A post-processing method that filters the nested 'files' collection after the
     * initial mapping is complete. This is automatically called by MapStruct.
     * <p>
     * It iterates through the DTO's 'files' list and removes any items that have
     * been soft-deleted, thus replicating the `where: { deleted_at: null }` clause.
     *
     * @param termination The source entity (unused here).
     * @param dto         The destination DTO, which will be modified in place.
     */
    @AfterMapping
    default void applyFiltering(Termination termination,
                                @MappingTarget TerminationDetailsDto dto) {
        if (dto.getFiles() != null) {
            dto.setFiles(dto.getFiles().stream().filter(file -> file.getDeletedAt() == null).toList());
        }
    }
}
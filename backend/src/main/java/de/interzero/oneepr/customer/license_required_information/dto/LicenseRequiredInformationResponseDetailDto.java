package de.interzero.oneepr.customer.license_required_information.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

/**
 * Represents the data for a license's required or general information.
 * This is a shallow DTO containing only direct properties from the entity,
 * as no nested includes were specified in the original Prisma query.
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LicenseRequiredInformationResponseDetailDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("setup_required_information_id")
    private Integer setupRequiredInformationId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private Instant deletedAt; // Needed for filtering by Contract<PERSON>apper

    @JsonProperty("question")
    private String question;

    @JsonProperty("file_id")
    private String fileId;

    @JsonProperty("answer")
    private String answer;

    @JsonProperty("setup_general_information_id")
    private Integer setupGeneralInformationId;

    @JsonProperty("type")
    private LicenseRequiredInformation.Type type;

    @JsonProperty("status")
    private LicenseRequiredInformation.Status status;

    @JsonProperty("kind")
    private LicenseRequiredInformation.Kind kind;

    // --- Mapped from transient getters in the entity ---

    @JsonProperty("license_id")
    private Integer licenseId;

    @JsonProperty("contract_id")
    private Integer contractId;
}
package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.contract.Contract;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDate;

/**
 * Data Transfer Object representing a projection of an Action Guide.
 * <p>
 * This DTO is a direct translation of the {@code select} clause on the
 * {@code action_guides} relation in the {@code customer.service.ts#details} method.
 */
@Data
@NoArgsConstructor
public class ActionGuideDetailsDto {

    @Schema(description = "The unique identifier of the country.")
    @JsonProperty("country_id")
    private Integer countryId;

    @Schema(description = "The ISO code of the country.")
    @JsonProperty("country_code")
    private String countryCode;

    @Schema(description = "The name of the country.")
    @JsonProperty("country_name")
    private String countryName;

    @Schema(description = "The flag icon or URL for the country.")
    @JsonProperty("country_flag")
    private String countryFlag;

    @Schema(description = "The status of the associated contract.")
    @JsonProperty("contract_status")
    private Contract.Status contractStatus;

    @Schema(description = "The associated termination details, if any.")
    @JsonProperty("termination")
    private TerminationDetailsDto termination;

    // Timestamps from the base ActionGuide model
    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "Timestamp of when the record was deleted, if applicable.")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;


}
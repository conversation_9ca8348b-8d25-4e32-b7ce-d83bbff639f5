package de.interzero.oneepr.customer.contract.dto;

import de.interzero.oneepr.customer.file.File;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * This DTO is a direct translation of the return object from the generateContractPdf
 * method in the NestJS source.
 * It is used as the response body for the PDF generation endpoint.
 * It encapsulates the generated PDF content and the corresponding file record.
 */
@Data
public class GeneratedContractPdfDto {

    @Schema(description = "The raw byte content of the generated PDF.")
    private final byte[] pdf;

    @Schema(description = "The database entity representing the created file.")
    private final File file;

    public GeneratedContractPdfDto(byte[] pdf,
                                   File file) {
        this.pdf = pdf;
        this.file = file;
    }

    public byte[] getPdf() {
        return pdf;
    }

    public File getFile() {
        return file;
    }
}
package de.interzero.oneepr.customer.termination.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.file.dto.FileDetailsDto;
import de.interzero.oneepr.customer.termination.Termination;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

/**
 * Represents the detailed data structure for a single termination record.
 * This DTO is designed to match the JSON structure returned by the Prisma query,
 * including its nested 'files' collection.
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TerminationDetailsDto {

    // --- Direct fields from the Termination entity ---

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @JsonProperty("completed_at")
    private Instant completedAt;

    @JsonProperty("requested_at")
    private Instant requestedAt;

    @JsonProperty("status")
    private Termination.Status status;

    // --- Nested collection from the 'include' block ---

    /**
     * Corresponds to the 'include: { files: ... }' block from the Prisma query.
     * This list is filtered by the TerminationMapper to exclude soft-deleted files.
     */
    @JsonProperty("files")
    private List<FileDetailsDto> files;
}
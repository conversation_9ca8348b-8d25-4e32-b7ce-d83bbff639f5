package de.interzero.oneepr.customer.license_packaging_service.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;

/**
 * Represents the data for a single packaging service associated with a license.
 * This DTO contains only the direct fields of the entity, matching the shallow
 * 'include: { packaging_services: true }' from the Prisma query.
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LicensePackagingServiceDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("setup_packaging_service_id")
    private Integer setupPackagingServiceId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @JsonProperty("obliged")
    private Boolean obliged;
}
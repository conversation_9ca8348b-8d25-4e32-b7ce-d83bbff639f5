package de.interzero.oneepr.customer.contract.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.contract.Contract;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * DTO for filtering a list of Contracts.
 * Origin: This DTO is a direct translation of the FindAllContractDto from the NestJS source.
 * Purpose: It is used to capture optional query parameters for the contract listing endpoint.
 * Function: Represents the optional criteria (customer_id, type, status) that can be used to filter contracts.
 */
@Data
public class FindAllContractDto {

    @Schema(
            description = "The customer id of the contract",
            example = "1",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("customer_id")
    private Integer customerId;

    @Schema(
            description = "The type of the contract",
            implementation = Contract.Type.class,
            example = "EU_LICENSE",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("type")
    private Contract.Type type;

    @Schema(
            description = "The status of the contract",
            implementation = Contract.Status.class,
            example = "ACTIVE",
            requiredMode = Schema.RequiredMode.NOT_REQUIRED
    )
    @JsonProperty("status")
    private Contract.Status status;
}
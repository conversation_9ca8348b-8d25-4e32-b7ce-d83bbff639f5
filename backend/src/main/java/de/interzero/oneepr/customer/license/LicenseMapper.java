package de.interzero.oneepr.customer.license;

import de.interzero.oneepr.customer.file.FileMapper;
import de.interzero.oneepr.customer.license.dto.LicenseDetailsDto;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingServiceMapper;
import de.interzero.oneepr.customer.termination.TerminationMapper;
import org.mapstruct.Mapper;

/**
 * A MapStruct interface for mapping a {@link License} entity to its detailed
 * {@link LicenseDetailsDto}. The implementation is generated automatically at compile time.
 * <p>
 * This mapper is configured to be a Spring component and uses other mappers to handle the
 * conversion of the nested objects and collections specified in the original Prisma query:
 * `include: { files: true, termination: true, price_list: true, packaging_services: true }`
 * <p>
 * No filtering is applied at this level, as none was specified in the nested include.
 */
@Mapper(
        componentModel = "spring",
        uses = {
                // List all the mappers needed for the nested DTO fields
                FileMapper.class, TerminationMapper.class, LicensePriceListMapper.class, LicensePackagingServiceMapper.class}
)
public interface LicenseMapper {


    /**
     * Maps a {@link License} entity to a {@link LicenseDetailsDto}.
     * <p>
     * MapStruct will automatically use the mappers declared in the `uses` clause
     * to handle the nested fields. For example, it will delegate to:
     * <ul>
     *   <li>{@link TerminationMapper} to convert the {@code termination} field.</li>
     *   <li>{@link FileMapper} to convert each item in the {@code files} list.</li>
     *   <li>{@link LicensePriceListMapper} to convert each item in the {@code priceList}.</li>
     *   <li>{@link LicensePackagingServiceMapper} to convert each item in the {@code packagingServices}.</li>
     * </ul>
     *
     * @param license The source License entity.
     * @return The fully mapped {@link LicenseDetailsDto}.
     */
    LicenseDetailsDto toDetailsDto(License license);

}
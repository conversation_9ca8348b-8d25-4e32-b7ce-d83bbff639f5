package de.interzero.oneepr.customer.contract;

import org.springframework.lang.NonNull;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * An in-memory implementation of {@link MultipartFile} that wraps a byte array.
 * This utility class is used to adapt a generated file (like a PDF byte array)
 * to the MultipartFile interface required by services like {@link de.interzero.oneepr.customer.file.FileService},
 * without needing to write the file to disk.
 */
public class ByteArrayMultipartFile implements MultipartFile {

    private final byte[] content;

    private final String name;

    private final String originalFilename;

    private final String contentType;

    /**
     * Constructs a new ByteArrayMultipartFile.
     *
     * @param content          The byte array content of the file.
     * @param name             The name of the parameter in the multipart form.
     * @param originalFilename The original filename in the client's filesystem.
     * @param contentType      The content type of the file.
     */
    public ByteArrayMultipartFile(byte[] content,
                                  String name,
                                  String originalFilename,
                                  String contentType) {
        this.content = content;
        this.name = name;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
    }

    @Override
    @NonNull
    public String getName() {
        return name;
    }

    @Override
    public String getOriginalFilename() {
        return originalFilename;
    }

    @Override
    public String getContentType() {
        return contentType;
    }

    @Override
    public boolean isEmpty() {
        return content == null || content.length == 0;
    }

    @Override
    public long getSize() {
        return content.length;
    }

    @Override
    @NonNull
    public byte[] getBytes() {
        return content;
    }

    @Override
    @NonNull
    public InputStream getInputStream() {
        return new ByteArrayInputStream(content);
    }

    @Override
    public void transferTo(@NonNull File dest) throws IOException, IllegalStateException {
        throw new UnsupportedOperationException("This operation is not supported for in-memory files.");
    }
}
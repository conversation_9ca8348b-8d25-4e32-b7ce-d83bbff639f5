package de.interzero.oneepr.customer.file;

import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.termination.Termination;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FileRepository extends JpaRepository<File, String> {

    /**
     * Find file by relation field and related entity ID
     * This method uses dynamic query to find files based on different relation fields
     *
     * @param relation   the relation field name (e.g., "required_information_id", "contract_id", etc.)
     * @param relativeId the ID of the related entity
     * @return Optional containing the file if found
     * @ts-legacy the condition should include AND f.deleted_at IS NULL
     */
    @Query(
            value = """
                    SELECT * FROM file f
                    WHERE CASE
                        WHEN :relation = 'required_information_id' THEN f.required_information_id = :relativeId
                        WHEN :relation = 'contract_id' THEN f.contract_id = :relativeId
                        WHEN :relation = 'certificate_id' THEN f.certificate_id = :relativeId
                        WHEN :relation = 'license_id' THEN f.license_id = :relativeId
                        WHEN :relation = 'termination_id' THEN f.termination_id = :relativeId
                        WHEN :relation = 'general_information_id' THEN f.general_information_id = :relativeId
                        WHEN :relation = 'third_party_invoice_id' THEN f.third_party_invoice_id = :relativeId
                        WHEN :relation = 'marketing_material_id' THEN f.marketing_material_id = :relativeId
                        WHEN :relation = 'partner_contract_id' THEN f.partner_contract_id = :relativeId
                        WHEN :relation = 'order_id' THEN f.order_id = :relativeId
                        ELSE FALSE
                    END
                    LIMIT 1
                    """,
            nativeQuery = true
    )
    Optional<File> findByRelation(@Param("relation") String relation,
                                  @Param("relativeId") Integer relativeId);

    /**
     * Finds all non-deleted files for a given list of contracts.
     *
     * @param contracts A list of {@link Contract} entities to find associated files for.
     * @return A list of {@link File} entities linked to the provided contracts.
     */
    @SuppressWarnings("UnusedReturnValue")
    List<File> findByContractInAndDeletedAtIsNull(List<Contract> contracts);

    /**
     * Finds all non-deleted files for a given list of licenses.
     *
     * @param licenses A list of {@link License} entities to find associated files for.
     * @return A list of {@link File} entities linked to the provided licenses.
     */
    @SuppressWarnings("UnusedReturnValue")
    List<File> findByLicenseInAndDeletedAtIsNull(List<License> licenses);

    /**
     * Finds all non-deleted files for a given list of terminations.
     *
     * @param terminations A list of {@link Termination} entities to find associated files for.
     * @return A list of {@link File} entities linked to the provided terminations.
     */
    @SuppressWarnings("UnusedReturnValue")
    List<File> findByTerminationInAndDeletedAtIsNull(List<Termination> terminations);
}

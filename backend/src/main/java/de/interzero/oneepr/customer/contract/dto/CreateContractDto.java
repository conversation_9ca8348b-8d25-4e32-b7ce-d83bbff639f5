package de.interzero.oneepr.customer.contract.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.contract.Contract;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;

/**
 * DTO for creating a new Contract.
 * Origin: This DTO is a direct translation of the CreateContractDto from the NestJS source.
 * Purpose: It is used as the request body for the contract creation endpoint.
 * Function: Represents the essential data required to create a new customer contract.
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CreateContractDto extends BaseDto {

    @Schema(
            description = "The customer id of the contract",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotNull
    @JsonProperty("customer_id")
    private Integer customerId;

    @Schema(
            description = "The type of the contract",
            example = "EU_LICENSE",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotNull
    @JsonProperty("type")
    private Contract.Type type;

    @Schema(
            description = "The title of the contract",
            example = "Contract Title",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotBlank
    @JsonProperty("title")
    private String title;

    @Schema(
            description = "The start date of the contract",
            example = "2024-03-20T00:00:00.000Z",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotNull
    @JsonProperty("start_date")
    private Instant startDate;
}
package de.interzero.oneepr.customer.license.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.customer.dto.TerminationDetailsDto;
import de.interzero.oneepr.customer.file.dto.FileDetailsDto;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_packaging_service.dto.LicensePackagingServiceDto;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

/**
 * Represents the detailed data structure for a single license, designed to match the
 * nested 'include' block from the original Prisma query within a contract search.
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LicenseDetailsDto {

    // --- Root fields from the License entity ---

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("registration_number")
    private String registrationNumber;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("country_code")
    private String countryCode;

    @JsonProperty("country_name")
    private String countryName;

    @JsonProperty("country_flag")
    private String countryFlag;

    @JsonProperty("year")
    private Integer year;

    @JsonProperty("start_date")
    private Instant startDate;

    @JsonProperty("end_date")
    private Instant endDate;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("registration_and_termination_monday_ref")
    private Integer registrationAndTerminationMondayRef;

    @JsonProperty("registration_status")
    private License.RegistrationStatus registrationStatus;

    @JsonProperty("clerk_control_status")
    private License.ClerkControlStatus clerkControlStatus;

    @JsonProperty("contract_status")
    private License.ContractStatus contractStatus;

    // --- Nested objects corresponding to the inner 'include' block ---

    /**
     * Corresponds to the 'include: { termination: true }' block.
     * Assumes a TerminationDetailsDto exists.
     */
    @JsonProperty("termination")
    private TerminationDetailsDto termination;

    /**
     * Corresponds to the 'include: { files: true }' block.
     * Assumes a FileDetailsDto exists. Note: Prisma query did not specify filtering
     * on deleted_at here, so all associated files would be included.
     */
    @JsonProperty("files")
    private List<FileDetailsDto> files;

    /**
     * Corresponds to the 'include: { price_list: true }' block.
     * Assumes a LicensePriceListDto exists.
     */
    @JsonProperty("price_list")
    private List<LicensePriceListDto> priceList;

    /**
     * Corresponds to the 'include: { packaging_services: true }' block.
     * Assumes a LicensePackagingServiceDto exists.
     */
    @JsonProperty("packaging_services")
    private List<LicensePackagingServiceDto> packagingServices;
}
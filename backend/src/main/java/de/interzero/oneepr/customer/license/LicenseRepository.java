package de.interzero.oneepr.customer.license;

import de.interzero.oneepr.customer.contract.Contract;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

@Repository
public interface LicenseRepository extends JpaRepository<License, Integer>, JpaSpecificationExecutor<License> {

    /**
     * Finds all licenses for a specific contract that have not been soft-deleted.
     *
     * @param contractId The ID of the contract to filter by.
     * @return A list of non-deleted licenses for the given contract.
     */
    List<License> findAllByContractIdAndDeletedAtIsNull(Integer contractId);

    /**
     * Performs a bulk update to reactivate multiple License entities at once.
     * <p>
     * This sets the contractStatus to 'ACTIVE' and updates the 'updatedAt' timestamp
     * for all licenses whose IDs are in the provided list.
     *
     * @param ids A list of License entity IDs to reactivate.
     * @param now The current timestamp to set for the 'updatedAt' field.
     * @ts-legacy This bulk update method is the direct JPA equivalent of the
     * {@code tx.license.updateMany({ where: { id: { in: ... } } })} operation
     * found in the TypeScript source code.
     */
    @Modifying
    @Query("UPDATE License l SET l.contractStatus = 'ACTIVE', l.updatedAt = :now WHERE l.id IN :ids")
    void reactivateAllByIds(@Param("ids") List<Integer> ids,
                            @Param("now") Instant now);

    /**
     * @ts-legacy This method directly translates a Prisma `updateMany` call that was used to synchronize the
     * registration status of all licenses sharing a common `monday_ref`. The original source contained a
     * TODO comment indicating a future database constraint should make this logic simpler.
     */
    @Modifying
    @Query("UPDATE License l SET l.registrationStatus = :status WHERE l.registrationAndTerminationMondayRef = :mondayRef")
    void updateRegistrationStatusByMondayRef(@Param("mondayRef") Integer mondayRef,
                                             @Param("status") License.RegistrationStatus status);

    Optional<License> findByIdAndDeletedAtIsNull(Integer id);

    /**
     * <p>Finds a license by customer email and license year.</p>
     *
     * @param email the email of the customer
     * @param year  the license year
     * @return an optional License entity if found
     */
    Optional<License> findByContractCustomerEmailAndYearAndDeletedAtIsNull(String email,
                                                                           Integer year);

    /**
     * Finds all non-deleted licenses for a given list of contracts.
     * The original NestJS code also included `price_list: true`, so this query
     * includes a JOIN FETCH for that relationship to replicate the behavior efficiently.
     *
     * @param contracts A list of {@link Contract} entities.
     * @return A list of {@link License} entities with their price lists initialized.
     */
    @Query("SELECT l FROM License l LEFT JOIN FETCH l.priceList WHERE l.contract IN :contracts AND l.deletedAt IS NULL")
    List<License> findByContractInAndDeletedAtIsNull(@Param("contracts") List<Contract> contracts);
}

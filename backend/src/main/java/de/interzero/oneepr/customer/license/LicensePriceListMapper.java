package de.interzero.oneepr.customer.license;

import de.interzero.oneepr.customer.entity.LicensePriceList;
import de.interzero.oneepr.customer.license.dto.LicensePriceListDto;
import org.mapstruct.Mapper;

/**
 * A MapStruct interface for mapping a {@link LicensePriceList} entity to its DTO.
 * The implementation of this interface is generated automatically at compile time.
 * <p>
 * This is a simple, direct mapping as the target DTO does not contain any
 * nested objects that require delegation to other mappers.
 */
@Mapper(componentModel = "spring")
public interface LicensePriceListMapper {

    /**
     * Maps a {@link LicensePriceList} entity to a {@link LicensePriceListDto}.
     * <p>
     * MapStruct will automatically map all fields with matching names. The parent
     * 'license' link in the entity is correctly ignored as it does not exist in the DTO.
     *
     * @param licensePriceList The source LicensePriceList entity.
     * @return The mapped {@link LicensePriceListDto}.
     */
    LicensePriceListDto toDto(LicensePriceList licensePriceList);
}
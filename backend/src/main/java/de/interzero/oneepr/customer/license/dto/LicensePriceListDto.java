package de.interzero.oneepr.customer.license.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.Map;

/**
 * Represents the data for a single price list item associated with a license.
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LicensePriceListDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("setup_price_list_id")
    private Integer setupPriceListId;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("condition_type")
    private String conditionType;

    @JsonProperty("condition_type_value")
    private String conditionTypeValue;

    @JsonProperty("start_date")
    private Instant startDate;

    @JsonProperty("end_date")
    private Instant endDate;

    @JsonProperty("basic_price")
    private Integer basicPrice;

    @JsonProperty("minimum_price")
    private Integer minimumPrice;

    @JsonProperty("registration_fee")
    private Integer registrationFee;

    @JsonProperty("handling_fee")
    private Integer handlingFee;

    @JsonProperty("variable_handling_fee")
    private Double variableHandlingFee;

    @JsonProperty("thresholds")
    private Map<String, Object> thresholds;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private Instant deletedAt;
}
package de.interzero.oneepr.customer.contract;

import de.interzero.oneepr.action_guide.ActionGuideRepository;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import de.interzero.oneepr.customer.certificate.CertificatePdfService;
import de.interzero.oneepr.customer.company.Company;
import de.interzero.oneepr.customer.contract.dto.*;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.entity.CompanyAddress;
import de.interzero.oneepr.customer.file.File;
import de.interzero.oneepr.customer.file.FileRepository;
import de.interzero.oneepr.customer.file.FileService;
import de.interzero.oneepr.customer.file.dto.CreateFileDto;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformationRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import de.interzero.oneepr.customer.termination.Termination;
import de.interzero.oneepr.customer.termination.TerminationRepository;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ContractService {

    private final ContractRepository contractRepository;

    private final CustomerRepository customerRepository;

    private final ModelMapper modelMapper;

    private final CertificatePdfService certificatePdfService;

    private final LicenseRequiredInformationRepository licenseRequiredInformationRepository;

    private final ActionGuideRepository actionGuideRepository;

    private final LicenseRepository licenseRepository;

    private final FileRepository fileRepository;

    private final TerminationRepository terminationRepository;

    private final FileService fileService;

    private final ContractMapper contractMapper;

    private static final String DEFAULT_FALLBACK = "---";

    private static final String CONTACT_NOT_FOUND = "Contract not found";

    private static final String CUSTOMER = "customer";

    /**
     * @param query DTO containing optional filters like customerId, status, and type.
     * @return A list of contracts matching the filters. If a customerId is provided,
     * related entities are eagerly fetched.
     * @ts-legacy The original Prisma query used a complex `include` block with nested `where` clauses to
     * filter related entities (e.g., only include non-deleted files). This is translated using a multi-query
     * approach. First, the primary `Contract` entities are fetched based on the filter criteria. Then, if a
     * customer ID was provided, separate queries are executed to fetch the filtered collections of related
     * entities (licenses, files, etc.) for all found contracts at once. This strategy correctly replicates
     * the filtered-include logic and prevents N+1 query problems.
     * <p>
     * Finds all contracts based on optional filter criteria.
     */
    @Transactional(readOnly = true)
    public List<ContractResponseDto> findAll(FindAllContractDto query) {
        // Build dynamic query using JPA Specifications
        Specification<Contract> spec = (root, criteriaQuery, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // where: deleted_at: null
            predicates.add(cb.isNull(root.get("deletedAt")));

            predicates.add(cb.notEqual(root.get("status"), Contract.Status.TERMINATED));

            // where: ...(query.customer_id && { customer_id: Number(query.customer_id) })
            if (query.getCustomerId() != null) {
                predicates.add(cb.equal(root.get(CUSTOMER).get("id"), query.getCustomerId()));
            }

            // where: ...(query.status && { status: query.status })
            if (query.getStatus() != null) {
                predicates.add(cb.equal(root.get("status"), query.getStatus()));
            }

            // where: ...(query.type && { type: query.type })
            if (query.getType() != null) {
                predicates.add(cb.equal(root.get("type"), query.getType()));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");

        // First query: fetch the parent contracts
        List<Contract> contracts = contractRepository.findAll(spec, sort);

        // If a customer_id is specified, fetch all related entities (the "include" logic)
        if (query.getCustomerId() != null && !contracts.isEmpty()) {
            // Fetch related collections in separate, efficient queries
            licenseRequiredInformationRepository.findByContractInAndDeletedAtIsNull(contracts);
            actionGuideRepository.findByContractInAndDeletedAtIsNullFetchingTermination(contracts);
            List<License> licenses = licenseRepository.findByContractInAndDeletedAtIsNull(contracts);
            fileRepository.findByContractInAndDeletedAtIsNull(contracts);
            List<Termination> contractTerminations = terminationRepository.findByContractInAndDeletedAtIsNull(contracts);

            if (!licenses.isEmpty()) {
                fileRepository.findByLicenseInAndDeletedAtIsNull(licenses);
                List<Termination> licenseTerminations = terminationRepository.findByLicenseInAndDeletedAtIsNull(licenses);
                if (!licenseTerminations.isEmpty()) {
                    fileRepository.findByTerminationInAndDeletedAtIsNull(licenseTerminations);
                }
            }
            if (!contractTerminations.isEmpty()) {
                fileRepository.findByTerminationInAndDeletedAtIsNull(contractTerminations);
            }
        }
        return contracts.stream().map(contractMapper::toDto).toList();
    }


    /**
     * Finds a single active contract by its ID.
     *
     * @param id The ID of the contract to find.
     * @return The found Contract entity.
     * @throws ResponseStatusException if the contract is not found.
     * @ts-legacy This method fetches the root Contract entity. Associated collections
     * (licenses, files, etc.) are available via lazy loading, but without the specific
     * 'deleted_at: null' filters applied in the original Prisma query. A direct
     * translation would require multiple, separate repository calls for each filtered collection.
     */
    @Transactional(readOnly = true)
    public ContractResponseDto findOne(Integer id) {
        Contract contract = contractRepository.findByIdAndDeletedAtIsNullAndStatusNot(id, Contract.Status.TERMINATED)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CONTACT_NOT_FOUND));
        // Use the mapper to transform the fully-loaded entity into the response DTO.
        // The mapper will handle all nested mappings and apply the necessary `deleted_at`
        // filtering on the collections, exactly replicating the Prisma query's logic.
        return contractMapper.toDto(contract);
    }

    /**
     * Creates a new contract.
     *
     * @param data The DTO containing the contract data.
     * @return The persisted Contract entity.
     */
    @Transactional
    public Contract create(CreateContractDto data) {
        Customer customer = customerRepository.findById(data.getCustomerId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found"));

        Contract contract = modelMapper.map(data, Contract.class);
        contract.setCustomer(customer);

        Calendar cal = GregorianCalendar.from(data.getStartDate().atZone(ZoneOffset.UTC));
        cal.set(Calendar.MONTH, Calendar.DECEMBER);
        cal.set(Calendar.DAY_OF_MONTH, 31);
        contract.setEndDate(cal.toInstant());
        contract.setCreatedAt(Instant.now());
        contract.setUpdatedAt(Instant.now());
        contract.setStatus(Contract.Status.ACTIVE);
        return contractRepository.save(contract);
    }

    /**
     * Updates an existing contract.
     *
     * @param id   The ID of the contract to update.
     * @param data The DTO with fields to update.
     * @param user The authenticated user performing the action.
     * @return The updated Contract entity.
     */
    @Transactional
    public Contract update(Integer id,
                           UpdateContractDto data,
                           AuthenticatedUser user) {
        validatingUserPermissionContracts(id, user);
        Contract existingContract = contractRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CONTACT_NOT_FOUND));
        ModelMapperConfig.mapPresentFields(data, existingContract);
        return contractRepository.save(existingContract);
    }

    /**
     * Soft-deletes a contract.
     *
     * @param id   The ID of the contract to delete.
     * @param user The authenticated user performing the action.
     */
    @Transactional
    public void remove(Integer id,
                       AuthenticatedUser user) {
        validatingUserPermissionContracts(id, user);
        Contract contract = contractRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CONTACT_NOT_FOUND));
        contract.setDeletedAt(LocalDate.now());
        contractRepository.save(contract);
    }

    /**
     * Generates a "Service Overview" PDF for a given contract.
     * This method fetches the contract and its licenses, transforms the data into a format
     * suitable for the PDF template, deletes any previous version of this file, generates
     * the new PDF, and uploads it as a file associated with the contract.
     *
     * @param contractId The ID of the contract.
     * @return A byte array containing the generated PDF content.
     * @throws ResponseStatusException if the contract is not found or the type is unsupported.
     * @throws IllegalStateException   if an unexpected error occurs during the process.
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @SuppressWarnings("java:S1301")
    public byte[] generateServiceOverviewPdf(Integer contractId) {
        try {
            // Step 1: Fetch the contract with all necessary related data using the optimized query.
            Contract contract = contractRepository.findByIdAndDeletedAtIsNullAndStatusNot(
                            contractId,
                            Contract.Status.TERMINATED)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CONTACT_NOT_FOUND));

            if (Objects.requireNonNull(contract.getType()) == Contract.Type.EU_LICENSE) {// Step 2: Transform license data into "rows" for the PDF template.
                // This mirrors the `contract.licenses.map(...)` logic from TypeScript.
                List<Map<String, Object>> rows = contract.getLicenses()
                        .stream()
                        .filter(license -> license.getDeletedAt() == null)
                        .map(license -> {
                            Map<String, Object> row = new HashMap<>();
                            row.put("country_name", license.getCountryName());
                            row.put("start_year", license.getYear());
                            row.put("end_year", license.getYear() + 1);

                            // Join packaging service names into a single string.
                            String obligation = license.getPackagingServices()
                                    .stream()
                                    .map(LicensePackagingService::getName)
                                    .collect(Collectors.joining(", "));
                            row.put("obligation", obligation);

                            // Safely access the first price list item and format fees.
                            license.getPriceList().stream().findFirst().ifPresentOrElse(
                                    priceList -> {
                                        // Use BigDecimal for accurate currency math and formatting.
                                        BigDecimal handlingFee = BigDecimal.valueOf(priceList.getHandlingFee())
                                                .divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);
                                        BigDecimal registrationFee = BigDecimal.valueOf(priceList.getRegistrationFee())
                                                .divide(new BigDecimal("100"), 3, RoundingMode.HALF_UP);

                                        row.put("handling_fee", handlingFee.toPlainString());
                                        row.put("registration_fee", registrationFee.toPlainString());
                                    }, () -> {
                                        // Provide default values if no price list exists.
                                        row.put("handling_fee", "0.000");
                                        row.put("registration_fee", "0.000");
                                    });

                            return row;
                        })
                        .toList();

                Map<String, Object> templateData = new HashMap<>();
                templateData.put("rows", rows);

                // Step 3: Generate the PDF using the appropriate service.
                byte[] pdfBytes = certificatePdfService.generatePdf(templateData, "templates/service-overview");

                // Step 4: Check for and delete the old version of the file, if it exists.
                contract.getFiles()
                        .stream()
                        .filter(file -> "service-overview.pdf".equals(file.getOriginalName()) && file.getDeletedAt() == null)
                        .findFirst()
                        .ifPresent(existingFile -> {
                            log.info("Deleting existing service overview file with ID: {}", existingFile.getId());
                            fileService.deleteFile(existingFile.getId());
                        });

                // Step 5: Upload the newly generated PDF.
                MultipartFile multipartFile = new ByteArrayMultipartFile(
                        pdfBytes,
                                                                         "file",
                                                                         "service-overview.pdf",
                                                                         "application/pdf");

                CreateFileDto createFileDto = new CreateFileDto();
                createFileDto.setContractId(contractId);
                createFileDto.setType(File.Type.CONTRACT);
                String userId = String.valueOf(contract.getCustomer().getUserId());

                File newFile = fileService.uploadFile(createFileDto, multipartFile, userId, CUSTOMER);
                log.info("Uploaded new service overview PDF. File ID: {}", newFile.getId());

                return pdfBytes;
            }
            throw new ResponseStatusException(
                    HttpStatus.BAD_REQUEST,
                                              "Service Overview PDF not implemented for this contract type.");
        } catch (ResponseStatusException e) {
            log.warn("Failed to generate service overview PDF for contractId {}: {}", contractId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error generating Service Overview PDF for contract ID: {}", contractId, e);
            throw new IllegalStateException("Error generating Service Overview PDF for contract ID: " + contractId, e);
        }
    }


    /**
     * @param contractId The ID of the contract to generate the PDF for.
     * @return A {@link GeneratedContractPdfDto} containing the PDF bytes and the created
     * {@link File} entity.
     * @throws ResponseStatusException if the contract is not found or if the contract
     *                                 type is not supported for PDF generation.
     * @throws IllegalStateException   if an unexpected error occurs during PDF generation or file upload.
     * @ts-legacy The original NestJS code called a fileService.uploadFile function that accepted the PDF buffer
     * directly. The provided Java {@link FileService} only accepts a {@link MultipartFile}. To bridge this, a
     * {@link ByteArrayMultipartFile} adapter is used to wrap the generated PDF's byte array, allowing it to be
     * passed to the existing FileService without modification.
     * <p>
     * Generates a PDF for a given contract, uploads it to storage, saves a corresponding
     * file record, and returns the PDF content along with the file record.
     */
    @SuppressWarnings("java:S1301")
    @Transactional
    public GeneratedContractPdfDto generateContractPdf(Integer contractId) {
        try {
            Contract contract = contractRepository.findById(contractId)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CONTACT_NOT_FOUND));

            if (Objects.requireNonNull(contract.getType()) == Contract.Type.EU_LICENSE) {
                Map<String, Object> data = new HashMap<>();
                Company firstCompany = contract.getCustomer().getCompanies().stream().findFirst().orElse(null);
                CompanyAddress address = Optional.ofNullable(firstCompany).map(Company::getAddress).orElse(null);

                data.put(
                        "company_name",
                        Optional.ofNullable(firstCompany).map(Company::getName).orElse(DEFAULT_FALLBACK));
                data.put(
                        "address",
                        Optional.ofNullable(address).map(CompanyAddress::getAddressLine).orElse(DEFAULT_FALLBACK));
                data.put(
                        "zip_code",
                        Optional.ofNullable(address).map(CompanyAddress::getZipCode).orElse(DEFAULT_FALLBACK));
                data.put(
                        "country_name",
                        Optional.ofNullable(address).map(CompanyAddress::getCountryCode).orElse(DEFAULT_FALLBACK));
                data.put(
                        "email",
                        Optional.ofNullable(contract.getCustomer()).map(Customer::getEmail).orElse(DEFAULT_FALLBACK));
                data.put("vat_id", Optional.ofNullable(firstCompany).map(Company::getVat).orElse(DEFAULT_FALLBACK));
                data.put("tax_id", Optional.ofNullable(firstCompany).map(Company::getTin).orElse(DEFAULT_FALLBACK));
                data.put("supplementary", DEFAULT_FALLBACK);
                data.put("generation_date", LocalDate.now().format(DateTimeFormatter.ofPattern("dd.MM.yyyy")));

                byte[] contractPdfBytes = certificatePdfService.generatePdf(data, "templates/contract.hbs");

                // Adapt byte[] to MultipartFile for the existing FileService
                MultipartFile contractPdfMultipartFile = new ByteArrayMultipartFile(
                        contractPdfBytes, "file", // This is the 'name' of the form field, can be a placeholder
                        "contract-eu.pdf", "application/pdf");

                // Prepare DTO for file upload
                CreateFileDto createFileDto = new CreateFileDto();
                createFileDto.setContractId(contractId);
                createFileDto.setType(File.Type.CONTRACT);

                String userId = String.valueOf(contract.getCustomer().getUserId());
                // Matches role in original NestJS code

                File contractFile = fileService.uploadFile(createFileDto, contractPdfMultipartFile, userId, CUSTOMER);

                log.info("Generated and uploaded contract PDF. File ID: {}", contractFile.getId());
                log.info("Original console log from NestJS: http://localhost:4000/files/{}", contractFile.getId());

                return new GeneratedContractPdfDto(contractPdfBytes, contractFile);
            }
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Contract pdf not implemented");
        } catch (ResponseStatusException e) {
            log.warn("Failed to generate contract PDF for contractId {}: {}", contractId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Error generating PDF for contract ID: {}", contractId, e);
            throw new IllegalStateException("Error generating PDF for contract ID: " + contractId, e);
        }
    }

    /**
     * Validates if a user has permission to access a contract.
     *
     * @param id   The ID of the contract.
     * @param user The authenticated user.
     * @throws ResponseStatusException if the contract/customer is not found or permission is denied.
     */
    public void validatingUserPermissionContracts(Integer id,
                                                  AuthenticatedUser user) {
        if (id == null || id <= 0) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Contract ID is invalid");
        }
        Contract contract = contractRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, CONTACT_NOT_FOUND));

        Customer customer = contract.getCustomer();
        if (customer == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Customer not found");
        }
        if (user.getRole() == Role.CUSTOMER) {
            int authenticatedUserId;
            try {
                authenticatedUserId = Integer.parseInt(user.getId());
            } catch (NumberFormatException ex) {
                throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Invalid user identifier");
            }
            if (!customer.getId().equals(authenticatedUserId)) {
                throw new ResponseStatusException(
                        HttpStatus.FORBIDDEN,
                                                  "You do not have permission to access this license contract");
            }

        }
    }
}
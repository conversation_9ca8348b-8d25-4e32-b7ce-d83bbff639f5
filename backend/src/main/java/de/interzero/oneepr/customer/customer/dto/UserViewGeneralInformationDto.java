package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.file.dto.FileDetailsDto;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * Data Transfer Object representing a general information item for the user profile view.
 * <p>
 * This DTO is a direct translation of the {@code LicenseRequiredInformation} model,
 * which is included under the alias {@code general_informations} within the
 * {@code customer.service.ts#findByUserId} method. It includes the nested
 * {@code answer_files} relation.
 */
@Data
@NoArgsConstructor
public class UserViewGeneralInformationDto {

    @Schema(description = "Unique identifier of the information record.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "ID of the corresponding setup item for required information.")
    @JsonProperty("setup_required_information_id")
    private Integer setupRequiredInformationId;

    @Schema(description = "The type of information required (e.g., TEXT, FILE).")
    @JsonProperty("type")
    private LicenseRequiredInformation.Type type;

    @Schema(description = "The status of the information request (e.g., OPEN, DONE).")
    @JsonProperty("status")
    private LicenseRequiredInformation.Status status;

    @Schema(description = "The name of the information item.")
    @JsonProperty("name")
    private String name;

    @Schema(description = "A description of the information item.")
    @JsonProperty("description")
    private String description;

    @Schema(description = "The question being asked to the user.")
    @JsonProperty("question")
    private String question;

    @Schema(description = "The user's answer to the question.")
    @JsonProperty("answer")
    private String answer;

    @Schema(description = "The kind of information (e.g., REQUIRED_INFORMATION).")
    @JsonProperty("kind")
    private LicenseRequiredInformation.Kind kind;

    @Schema(description = "ID of the corresponding setup item for general information.")
    @JsonProperty("setup_general_information_id")
    private Integer setupGeneralInformationId;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "List of files provided as an answer.")
    @JsonProperty("answer_files")
    private List<FileDetailsDto> answerFiles;
}
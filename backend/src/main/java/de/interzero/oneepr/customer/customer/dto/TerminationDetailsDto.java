package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.file.dto.FileDetailsDto;
import de.interzero.oneepr.customer.termination.Termination;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * Data Transfer Object representing the details of a service termination.
 * <p>
 * This DTO is a direct translation of the {@code Termination} model, included with
 * its non-deleted files, as defined in the {@code customer.service.ts#details} method.
 */
@Data
@NoArgsConstructor
public class TerminationDetailsDto {

    @Schema(description = "Unique identifier of the termination record.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "Timestamp of when the termination was completed.")
    @JsonProperty("completed_at")
    private Instant completedAt;

    @Schema(description = "Timestamp of when the termination was requested.")
    @JsonProperty("requested_at")
    private Instant requestedAt;

    @Schema(description = "Current status of the termination.")
    @JsonProperty("status")
    private Termination.Status status;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "List of files associated with this termination.")
    @JsonProperty("files")
    private List<FileDetailsDto> files;
}
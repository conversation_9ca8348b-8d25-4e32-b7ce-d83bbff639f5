package de.interzero.oneepr.customer.contract.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.customer.dto.ActionGuideDetailsDto;
import de.interzero.oneepr.customer.customer.dto.CustomerDetailsDto;
import de.interzero.oneepr.customer.file.dto.FileDetailsDto;
import de.interzero.oneepr.customer.license.dto.LicenseDetailsDto;
import de.interzero.oneepr.customer.license_required_information.dto.LicenseRequiredInformationResponseDetailDto;
import de.interzero.oneepr.customer.termination.dto.TerminationDetailsDto;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.List;

/**
 * Represents the detailed data structure for a single contract response.
 * This DTO is designed to be a direct Java equivalent of the JSON object returned
 * by the TypeScript ContractService's findOne method, including all its nested 'include' blocks.
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContractResponseDto {

    // --- Root fields from the Contract entity ---

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("title")
    private String title;

    @JsonProperty("start_date")
    private Instant startDate;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("end_date")
    private Instant endDate;

    @JsonProperty("type")
    private Contract.Type type;

    @JsonProperty("status")
    private Contract.Status status;


    // --- Nested objects corresponding to the Prisma 'include' block ---

    @JsonProperty("customer")
    private CustomerDetailsDto customer;

    @JsonProperty("action_guides")
    private List<ActionGuideDetailsDto> actionGuides;

    @JsonProperty("licenses")
    private List<LicenseDetailsDto> licenses;

    @JsonProperty("general_informations")
    private List<LicenseRequiredInformationResponseDetailDto> generalInformations;

    @JsonProperty("files")
    private List<FileDetailsDto> files;

    @JsonProperty("termination")
    private TerminationDetailsDto termination;

}
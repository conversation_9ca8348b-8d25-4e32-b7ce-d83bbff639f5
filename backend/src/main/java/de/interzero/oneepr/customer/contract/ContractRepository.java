package de.interzero.oneepr.customer.contract;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ContractRepository extends JpaRepository<Contract, Integer>, JpaSpecificationExecutor<Contract> {

    List<Contract> findByCustomerIdAndDeletedAtIsNull(Integer customerId);


    /**
     * Finds a single non-deleted Contract by its ID, eagerly fetching the associated Customer entity.
     *
     * @param id The ID of the contract to find.
     * @return An Optional containing the Contract.
     */
    Optional<Contract> findWithCustomerByIdAndDeletedAtIsNull(Integer id);

    /**
     * Finds all non-deleted contracts for a given customer ID, eagerly fetching all nested
     * required information for both the contract (general) and its licenses (country-specific).
     *
     * @param customerId The ID of the customer whose contracts are to be fetched.
     * @return A list of contracts with their related information.
     */
    List<Contract> findAllWithRequiredInformationByCustomerIdAndDeletedAtIsNull(Integer customerId);

    @Query(
            "SELECT c FROM Contract c LEFT JOIN FETCH c.licenses l LEFT JOIN FETCH l.packagingServices ps LEFT JOIN FETCH ps.volumeReports vr LEFT JOIN FETCH vr.volumeReportItems vri WHERE c.customer.id = :customerId AND c.status = 'ACTIVE' AND c.type = 'DIRECT_LICENSE' AND c.deletedAt IS NULL AND l.deletedAt IS NULL AND ps.deletedAt IS NULL AND vri.deletedAt IS NULL ORDER BY l.year ASC"
    )
    Optional<Contract> findActiveDirectLicenseContractWithDetails(@Param("customerId") Integer customerId);

    /**
     * Finds all contracts by customer's email, where the customer is not deleted.
     *
     * @param email the email of the customer
     * @return list of contracts with related licenses and action guides
     */
    List<Contract> findByCustomerEmailAndCustomerDeletedAtIsNull(String email);

    /**
     * Finds a single active contract by its ID.
     * An active contract is one that is not soft-deleted and does not have a 'TERMINATED' status.
     *
     * @param id The ID of the contract.
     * @return An Optional containing the found Contract, or empty if not found.
     */
    Optional<Contract> findByIdAndDeletedAtIsNullAndStatusNot(Integer id,
                                                              Contract.Status status);

}

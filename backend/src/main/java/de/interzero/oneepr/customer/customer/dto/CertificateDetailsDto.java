package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.certificate.Certificate;
import de.interzero.oneepr.customer.file.dto.FileDetailsDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * Data Transfer Object representing the details of a license certificate.
 * <p>
 * This DTO is a direct translation of the {@code Certificate} model, included with
 * its non-deleted files, as defined within the {@code licenses} relation of the
 * {@code customer.service.ts#details} method.
 */
@Data
@NoArgsConstructor
public class CertificateDetailsDto {

    @Schema(description = "Unique identifier of the certificate record.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The name of the certificate.")
    @JsonProperty("name")
    private String name;

    @Schema(description = "The status of the certificate.")
    @JsonProperty("status")
    private Certificate.Status status;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "List of files associated with this certificate.")
    @JsonProperty("files")
    private List<FileDetailsDto> files;
}
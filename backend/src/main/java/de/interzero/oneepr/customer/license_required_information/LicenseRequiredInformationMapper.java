package de.interzero.oneepr.customer.license_required_information;

import de.interzero.oneepr.customer.license_required_information.dto.LicenseRequiredInformationResponseDetailDto;
import de.interzero.oneepr.customer.license_required_information.dto.LicenseRequiredInformationResponseDto;
import org.mapstruct.Mapper;

/**
 * A MapStruct interface for mapping a {@link LicenseRequiredInformation} entity to its DTO.
 * The implementation of this interface is generated automatically at compile time.
 * <p>
 * This is a simple, direct mapping as the target DTO does not contain any
 * nested objects that require delegation to other mappers.
 */
@Mapper(componentModel = "spring")
public interface LicenseRequiredInformationMapper {

    /**
     * Maps a {@link LicenseRequiredInformation} entity to a {@link LicenseRequiredInformationResponseDto}.
     * <p>
     * MapStruct will automatically map all fields with matching names. It will also
     * correctly use the transient getter methods in the entity (e.g., {@code getLicenseId()})
     * as the source for the corresponding fields in the DTO.
     *
     * @param licenseRequiredInformation The source LicenseRequiredInformation entity.
     * @return The mapped {@link LicenseRequiredInformationResponseDto}.
     */
    LicenseRequiredInformationResponseDetailDto toResponseDto(LicenseRequiredInformation licenseRequiredInformation);
}
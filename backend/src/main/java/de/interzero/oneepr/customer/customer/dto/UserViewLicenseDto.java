package de.interzero.oneepr.customer.customer.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.file.dto.FileDetailsDto;
import de.interzero.oneepr.customer.license.License;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * Data Transfer Object representing a detailed view of a License for the user profile.
 * <p>
 * This DTO is a direct translation of the {@code License} model and its nested
 * relations as defined in the {@code licenses} include block of the
 * {@code customer.service.ts#findByUserId} method.
 */
@Data
@NoArgsConstructor
public class UserViewLicenseDto {

    @Schema(description = "Unique identifier of the license.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The license registration number.")
    @JsonProperty("registration_number")
    private String registrationNumber;

    @Schema(description = "The registration status of the license.")
    @JsonProperty("registration_status")
    private License.RegistrationStatus registrationStatus;

    @Schema(description = "The clerk control status of the license.")
    @JsonProperty("clerk_control_status")
    private License.ClerkControlStatus clerkControlStatus;

    @Schema(description = "The contract status of the license.")
    @JsonProperty("contract_status")
    private License.ContractStatus contractStatus;

    @Schema(description = "The unique identifier of the country.")
    @JsonProperty("country_id")
    private Integer countryId;

    @Schema(description = "The ISO code of the country.")
    @JsonProperty("country_code")
    private String countryCode;

    @Schema(description = "The name of the country.")
    @JsonProperty("country_name")
    private String countryName;

    @Schema(description = "The flag icon or URL for the country.")
    @JsonProperty("country_flag")
    private String countryFlag;

    @Schema(description = "The year the license is valid for.")
    @JsonProperty("year")
    private Integer year;

    @Schema(description = "The start date of the license.")
    @JsonProperty("start_date")
    private Instant startDate;

    @Schema(description = "The end date of the license, if applicable.")
    @JsonProperty("end_date")
    private Instant endDate;

    @Schema(description = "Timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of when the record was last updated.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "Reference to a Monday.com board for registration and termination.")
    @JsonProperty("registration_and_termination_monday_ref")
    private Integer registrationAndTerminationMondayRef;

    @Schema(description = "List of all files associated with this license.")
    @JsonProperty("files")
    private List<FileDetailsDto> files;

    @Schema(description = "The associated termination details, if any.")
    @JsonProperty("termination")
    private TerminationDetailsDto termination;

    @Schema(description = "List of price list items for this license.")
    @JsonProperty("price_list")
    private List<UserViewLicensePriceListDto> priceList;

    @Schema(description = "List of packaging services for this license.")
    @JsonProperty("packaging_services")
    private List<UserViewLicensePackagingServiceDto> packagingServices;
}
package de.interzero.oneepr.customer.contract;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.contract.dto.CreateContractDto;
import de.interzero.oneepr.customer.contract.dto.UpdateContractDto;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;


/**
 * Integration tests for the {@link ContractController}.
 * <p>
 * This class uses {@link SpringBootTest} to test the full application stack,
 * from the controller down to the database. It sets up real data and verifies
 * the complete behavior of the API endpoints for happy path scenarios.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ContractControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    // Repositories for setting up test data
    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private EntityManager entityManager;

    private Customer testCustomer;

    private Contract testContract;

    @BeforeEach
    void setUp() {
        // 1. Create a parent Customer for the contracts
        testCustomer = new Customer();
        testCustomer.setFirstName("Test");
        testCustomer.setLastName("User");
        testCustomer.setEmail("<EMAIL>");
        testCustomer.setUserId(999);
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer = customerRepository.save(testCustomer);

        // 2. Create a primary Contract for testing
        testContract = new Contract();
        testContract.setCustomer(testCustomer);
        testContract.setTitle("Main Test Contract");
        testContract.setType(Contract.Type.EU_LICENSE);
        testContract.setStatus(Contract.Status.ACTIVE);
        testContract.setStartDate(Instant.now());
        testContract.setEndDate(Instant.now().plus(365, ChronoUnit.DAYS));
        testContract.setCreatedAt(Instant.now());
        testContract.setUpdatedAt(Instant.now());
        testCustomer.addContract(testContract);
        testContract = contractRepository.save(testContract);

        // 3. Create another contract to test the findAll endpoint
        Contract anotherContract = new Contract();
        anotherContract.setCustomer(testCustomer);
        anotherContract.setTitle("Another Contract");
        anotherContract.setType(Contract.Type.DIRECT_LICENSE);
        anotherContract.setStatus(Contract.Status.ACTIVE);
        anotherContract.setStartDate(Instant.now());
        anotherContract.setEndDate(Instant.now().plus(365, ChronoUnit.DAYS));
        anotherContract.setCreatedAt(Instant.now());
        anotherContract.setUpdatedAt(Instant.now());
        testCustomer.addContract(anotherContract);
        contractRepository.save(anotherContract);
        entityManager.flush();
        entityManager.clear();


    }

    @Test
    @WithMockUser(roles = TestRole.ADMIN)
    void findAll_whenCalled_shouldReturnListOfContracts() throws Exception {
        mockMvc.perform(get(Api.CONTRACTS).param("customerId", testCustomer.getId().toString()))
                .andExpect(status().isOk())
                .andDo(print()).andExpect(jsonPath("$", hasSize(2)));
    }

    @Test
    @WithMockUser(roles = TestRole.ADMIN)
    void findOne_whenGivenValidId_shouldReturnDetailedContract() throws Exception {
        mockMvc.perform(get(Api.CONTRACTS + "/" + testContract.getId()))
                .andExpect(status().isOk())
                .andDo(print())
                .andExpect(jsonPath("$.id", is(testContract.getId())))
                .andExpect(jsonPath("$.title", is("Main Test Contract")))
                .andExpect(jsonPath("$.customer.id", is(testCustomer.getId())));
    }

    @Test
    @WithMockUser(roles = TestRole.ADMIN)
    void create_whenGivenValidDto_shouldReturnCreatedContract() throws Exception {
        CreateContractDto createDto = new CreateContractDto();
        createDto.setCustomerId(testCustomer.getId());
        createDto.setTitle("Newly Created Contract");
        createDto.setType(Contract.Type.ACTION_GUIDE);
        createDto.setStartDate(Instant.now());

        mockMvc.perform(post(Api.CONTRACTS).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.title", is("Newly Created Contract")))
                .andExpect(jsonPath("$.customer.id", is(testCustomer.getId())));
    }

    @Test
    @WithMockUser(
            username = "999",
            roles = TestRole.ADMIN
    )
    void update_whenGivenValidIdAndDto_shouldReturnUpdatedContract() throws Exception {
        UpdateContractDto updateDto = new UpdateContractDto();
        updateDto.setTitle("An Updated Title");

        mockMvc.perform(put(Api.CONTRACTS + "/" + testContract.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testContract.getId())))
                .andExpect(jsonPath("$.title", is("An Updated Title")));
    }

    @Test
    @WithMockUser(
            username = "999",
            roles = TestRole.ADMIN
    )
    void remove_whenGivenValidId_shouldSoftDeleteAndReturnNoContent() throws Exception {
        mockMvc.perform(delete(Api.CONTRACTS + "/" + testContract.getId())).andExpect(status().isNoContent());

        // Verify the entity was actually soft-deleted in the database
        Optional<Contract> deletedContract = contractRepository.findById(testContract.getId());
        assertNotNull(deletedContract.orElseThrow().getDeletedAt());
    }
}